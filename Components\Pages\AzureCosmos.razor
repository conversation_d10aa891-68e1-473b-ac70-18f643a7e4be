@page "/azureCosmos"
@rendermode InteractiveServer
@using ApexCharts
@using System.Linq
@using System.Collections.Generic
@using System.Drawing
@using System.Data
@using Dashboard_Yuntech.Models.AzureCosmos
@using Dashboard_Yuntech.Models.ChartModels
@using Dashboard_Yuntech.Service
@using Newtonsoft.Json
@using System.Reflection
@inject ChartDataService _chartDataService
@inject ChartConfigurationService _configService
@inject AzureCosmosService _cosmosService
@inject ExcelService ExcelService

<PageTitle>雲科指標-圖資處</PageTitle>
<MyPageTitle Title="雲科圖資處"></MyPageTitle>


@if (errorMessage != null)
{
    <div class="alert alert-danger">
        @errorMessage
    </div>
}

@if (isLoading)
{
    <div class="text-center mt-5">
        <div class="spinner-border mt-5" role="status">
            <span class="visually-hidden">載入中...</span>
        </div>
        <p class="mt-3">正在載入 Azure Cosmos DB 數據...</p>
    </div>
}
else
{
    <nav>
        <div class="nav nav-tabs elegant-tabs" id="nav-tab-elegant" role="tablist">
            <button class="nav-link elegant-tab active" id="nav-Lib-tab-elegant" data-bs-toggle="tab" data-bs-target="#nav-Lib-elegant" type="button" role="tab">
                <span class="tab-text">📚 圖書館</span>
            </button>
            <button class="nav-link elegant-tab" id="nav-Tcs-tab-elegant" data-bs-toggle="tab" data-bs-target="#nav-Tcs-elegant" type="button" role="tab">
                <span>💻 資訊中心</span>
            </button>
        </div>
    </nav>
    <div class="tab-content elegant-content pt-4" id="nav-tabContent-elegant">
        <!--圖書館-->
        <div class="tab-pane fade show active" id="nav-Lib-elegant" role="tabpanel">
            <MyAlert Text="圖書館 | 館藏指標"></MyAlert>
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-4 mb-4">
                <!-- 資源統計 -->
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                                TItem="UA_LibData"
                                ContainerType="@ContainerType.UA_Lib"
                                CosmosDataPropertyName="UA_LibData"
                                Title="⭐ 113學年度重點館藏資源統計"
                                ChartOptions="@Lib_ChEn_PieChartOpt"
                                ChartType="SeriesType.Pie"
                                SeriesName="藏書比例"
                                XValueSelector="@(e => e.SchoolName)"
                                YValueSelector="@(e => (decimal?)e.Lib_Ch_Total)"
                                ModalId="Lib_ChEn_Pie"
                                PreChartData="@pre_UALibData"
                                TargetSchoolName="@filterYuntech"
                                TableData="@filteredLibChTotalTableData"
                                OnDataLoaded="@Lib_ChartLoad"
                                PieChartFields="@libPieChartFields"
                                EnableClickMeButton="true" />
                </div>

                <!-- 紙本圖書 -->
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="📚 紙本圖書▸中文藏書量"
                    ChartOptions="@Lib_Ch_AllType_YunTechOpt"
                    ChartType="SeriesType.Area"
                    SeriesName="中文藏書量"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_Ch_Total)"
                    ModalId="Lib_Ch_AllType_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad"
                    EnableClickMeButton="true" />
                </div>
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="📚 紙本圖書▸英文藏書量"
                    ChartOptions="@Lib_En_Total_YunTechOpt"
                    ChartType="SeriesType.Area"
                    SeriesName="英文藏書量"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_En_Total)"
                    ModalId="Lib_En_Total_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
            </div>

            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-4 mb-4">
                <!-- 期刊 -->
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="📰 期刊▸期刊合訂本"
                    ChartOptions="@Lib_BoundJournals_YunTechOpt"
                    ChartType="SeriesType.Bar"
                    SeriesName="期刊合訂本"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_BoundJournals)"
                    ModalId="Lib_BJ_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="📰 期刊▸中、日文期刊"
                    ChartOptions="@Lib_Paper_ChJP_YunTechOpt"
                    ChartType="SeriesType.Bar"
                    SeriesName="中、日文期刊"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_Paper_ChJP)"
                    ModalId="Lib_Paper_ChJP_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="📰 期刊▸西文期刊"
                    ChartOptions="@Lib_Paper_En_YunTechOpt"
                    ChartType="SeriesType.Bar"
                    SeriesName="西文期刊"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_Paper_En)"
                    ModalId="Lib_Paper_En_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
                <!-- E化資源 -->
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="💻 E化資源▸電子期刊"
                    ChartOptions="@Lib_E_Journals_YunTechOpt"
                    ChartType="SeriesType.Area"
                    SeriesName="電子期刊"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_E_Journals)"
                    ModalId="Lib_E_Journals_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad"
                    EnableClickMeButton="true" />
                </div>
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="💻 E化資源▸資料庫"
                    ChartOptions="@Lib_E_DB_YunTechOpt"
                    ChartType="SeriesType.Area"
                    SeriesName="資料庫"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_E_DB)"
                    ModalId="Lib_E_DB_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="💻 E化資源▸電子書"
                    ChartOptions="@Lib_E_Book_YunTechOpt"
                    ChartType="SeriesType.Area"
                    SeriesName="電子書"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_E_Book)"
                    ModalId="Lib_E_Book_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
            </div>

            <!-- 非書資料 -->
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-4 mb-4">
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="💿 非書資料▸視聽資料"
                    ChartOptions="@Lib_AV_YunTechOpt"
                    ChartType="SeriesType.Bar"
                    SeriesName="視聽資料"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_AV)"
                    ModalId="Lib_AV_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="💿 非書資料▸報紙"
                    ChartOptions="@Lib_Paper_YunTechOpt"
                    ChartType="SeriesType.Bar"
                    SeriesName="電子書"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_Paper)"
                    ModalId="Lib_Paper_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
            </div>

            <!--讀者服務指標-->
            <MyAlert Text="圖書館 | 讀者服務指標"></MyAlert>
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-4 mb-4">
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="借書人次"
                    ChartOptions="@Lib_Usr_YunTechOpt"
                    ChartType="SeriesType.Bar"
                    SeriesName="借書人次"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_User)"
                    ModalId="Lib_Usr_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="圖書借閱冊次"
                    ChartOptions="@Lib_Lend_YunTechOpt"
                    ChartType="SeriesType.Bar"
                    SeriesName="圖書借閱冊次"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_Lend)"
                    ModalId="Lib_Lend_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
                <div class="col">
                    <AzureChart TContainer="UA_LibModel"
                    TItem="UA_LibData"
                    ContainerType="@ContainerType.UA_Lib"
                    CosmosDataPropertyName="UA_LibData"
                    Title="線上及光碟資料庫檢索人次"
                    ChartOptions="@Lib_OnlineUse_YunTechOpt"
                    ChartType="SeriesType.Bar"
                    SeriesName="線上檢索人次"
                    XValueSelector="@(e => e.AcademicYear)"
                    YValueSelector="@(e => (decimal?)e.Lib_OnlineUse)"
                    ModalId="Lib_OnlineUse_YunTech"
                    PreChartData="@pre_UALibData"
                    TargetSchoolName="@filterYuntech"
                    TableData="@filteredLibChTotalTableData"
                    OnDataLoaded="@Lib_ChartLoad" />
                </div>
            </div>

            <!--圖書資源經費運用效益指標-->
            <MyAlert Text="圖書館 | 圖書資源經費運用效益指標"></MyAlert>
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-4 mb-4">
                <div class="col">
                    <ExcelChart TItem="UA_LibExcel_Money"
                    Title="圖書資源經費"
                    ExcelUrl="@Xlsx_TCSForm"
                    TableData="@tcsFormTableData"
                    TableColumns="@tcsFormTableColumns"
                    ChartData="@tcsFormChartData"
                    ChartOptions="@tcsFormChartOptions"
                    SeriesName="圖書資源經費"
                    XValueSelector="@(e => e.Year)"
                    YValueSelector="@(e => (decimal?)e.Lib_Money)"
                    ChartType="SeriesType.Area"
                    ModalId="Lib_Money_Line"
                    OnDataLoaded="@HandleTCSFormDataLoaded"
                    EnableClickMeButton="true" />
                </div>

                <div class="col">
                    <ExcelChart TItem="UA_LibExcel_Money"
                    Title="註冊學生人數"
                    ExcelUrl="@Xlsx_TCSForm"
                    TableData="@tcsFormTableData"
                    TableColumns="@tcsFormTableColumns"
                    ChartData="@studCountChartData"
                    ChartOptions="@studCountChartOptions"
                    SeriesName="註冊學生人數"
                    XValueSelector="@(e => e.Year)"
                    YValueSelector="@(e => (decimal?)e.Lib_StudCount)"
                    ChartType="SeriesType.Area"
                    ModalId="Lib_StudCount_Line"
                    OnDataLoaded="@HandleTCSFormDataLoaded" />
                </div>

                <div class="col">
                    <ExcelChart TItem="UA_LibExcel_Money"
                    Title="學生平均享有圖資經費"
                    ExcelUrl="@Xlsx_TCSForm"
                    TableData="@tcsFormTableData"
                    TableColumns="@tcsFormTableColumns"
                    ChartData="@studPerMoneyChartData"
                    ChartOptions="@studPerMoneyChartOptions"
                    SeriesName="學生平均享有圖資經費"
                    XValueSelector="@(e => e.Year)"
                    YValueSelector="@(e => (decimal?)e.Lib_StudPerMoney)"
                    ChartType="SeriesType.Area"
                    ModalId="Lib_StudPerMoney_Line"
                    OnDataLoaded="@HandleTCSFormDataLoaded" />
                </div>
            </div>

            <!--圖書資源經費運用效益指標-->
            <MyAlert Text="圖書館 | 圖書資源經費運用效益指標"></MyAlert>
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-4 mb-4">
                <!-- 專業館員人力指標長條圖 -->
                <div class="col">
                    <ExcelChart TItem="UA_LibExcel_Worker"
                    Title="專業館員人力指標"
                    ExcelUrl="@Xlsx_Worker"
                    TableData="@workerTableData"
                    TableColumns="@workerTableColumns"
                    ChartData="@workerChartData"
                    ChartOptions="@workerChartOptions"
                    SeriesName="國考合格人員"
                    XValueSelector="@(e => e.Year)"
                    YValueSelector="@(e => (decimal?)e.Lib_CertifiedStaff)"
                    ChartType="SeriesType.Area"
                    ModalId="Lib_CertifiedStaff_Area"
                    MultiSeries="true"
                    OnDataLoaded="@HandleWorkerDataLoaded" />
                </div>
            </div>
        </div>

        <!--資訊中心-->

        <div class="tab-pane fade" id="nav-Tcs-elegant" role="tabpanel">
            <!-- 系統組 -->
            <MyAlert Text="資訊中心 | 系統組"></MyAlert>
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-4 mb-4">
                <div class="col">
                    <ExcelChart TItem="TIS_SysLoginCnt"
                    Title="2025/05 系統登入次數分布"
                    ExcelUrl="@Xlsx_Login"
                    TableData="@loginTableData"
                    TableColumns="@loginTableColumns"
                    ChartData="@loginChartData"
                    ChartOptions="@loginChartOptions"
                    SeriesName="系統登入次數"
                    XValueSelector="@(e => e.REQ_APPID)"
                    YValueSelector="@(e => (decimal?)e.TIS_LoginCnt)"
                    ChartType="SeriesType.Treemap"
                    ModalId="login_count_treemap"
                    OnDataLoaded="@HandleLoginDataLoaded"
                    EnableClickMeButton="true" />
                </div>

                <div class="col">
                    <ExcelChart TItem="TIS_SysDevCnt"
                    Title="自行開發系統數量統計"
                    ExcelUrl="@Xlsx_SysCount"
                    TableData="@sysCountTableData"
                    TableColumns="@sysCountTableColumns"
                    ChartData="@sysCountChartData"
                    ChartOptions="@sysCountChartOptions"
                    SeriesName="開發系統數"
                    XValueSelector="@(e => e.Time)"
                    YValueSelector="@(e => (decimal?)e.TIS_SysCnt)"
                    ChartType="SeriesType.Area"
                    ModalId="sys_count_area"
                    OnDataLoaded="@HandleSysCountDataLoaded" />
                </div>

                <div class="col">
                    <ExcelChart TItem="TIS_Form"
                    Title="系統組三大需求表統計"
                    ExcelUrl="@Xlsx_TCSFormNew"
                    TableData="@tcsFormTableDataNew"
                    TableColumns="@tcsFormTableColumnsNew"
                    ChartData="@tcsFormChartDataNew"
                    ChartOptions="@tcsFormChartOptionsNew"
                    SeriesName="需求數量"
                    XValueSelector="@(e => e.FormType)"
                    YValueSelector="@(e => (decimal?)e.TCS_FormCnt)"
                    ChartType="SeriesType.Bar"
                    ModalId="tcs_form_bar_new"
                    OnDataLoaded="@HandleTCSFormNewDataLoaded" />
                </div>
            </div>

            <!-- 應用組 -->
            <MyAlert Text="資訊中心 | 應用組"></MyAlert>
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-3 g-4 mb-4">
                <div class="col">
                    <ExcelChart TItem="TIM_GPURate"
                    Title="歷年GPU使用率(%)"
                    ExcelUrl="@Xlsx_GPURate"
                    TableData="@gpuRateTableData"
                    TableColumns="@gpuRateTableColumns"
                    ChartData="@gpuRateChartData"
                    ChartOptions="@gpuRateChartOptions"
                    SeriesName="GPU使用率"
                    XValueSelector="@(e => e.Time)"
                    YValueSelector="@(e => (decimal?)e.TIM_GPURateP)"
                    ChartType="SeriesType.Line"
                    ModalId="gpu_rate_line"
                    OnDataLoaded="@HandleGPURateDataLoaded" />
                </div>
                <!-- GPU使用人數 (混合圖表) -->
                <div class="col">
                    <ExcelChart TItem="TIM_GPUser"
                    Title="歷年GPU使用人數"
                    ExcelUrl="@Xlsx_GPUser"
                    TableData="@gpuUserTableData"
                    TableColumns="@gpuUserTableColumns"
                    ChartData="@gpuUserChartData"
                    ChartOptions="@gpuUserChartOptions"
                    SeriesName="GPU使用人數"
                    XValueSelector="@(e => e.Time)"
                    YValueSelector="@(e => (decimal?)e.TIM_GPU_UserCnt)"
                    ChartType="SeriesType.Line"
                    ModalId="gpu_user_count_line"
                    MultiSeries="true"
                    IsMixedChart="true"
                    SeriesTypes="@gpuUserChartSeriesTypes"
                    isShowDataLabels="false"
                    OnDataLoaded="@HandleGPUUserDataLoaded" />
                </div>

                <div class="col">
                    <ExcelChart TItem="TIM_GPUser"
                    Title="歷年GPU使用人次統計"
                    ExcelUrl="@Xlsx_GPUser"
                    TableData="@gpuUser2TableData"
                    TableColumns="@gpuUser2TableColumns"
                    ChartData="@gpuUser2ChartData"
                    ChartOptions="@gpuUser2ChartOptions"
                    SeriesName="GPU使用人次"
                    XValueSelector="@(e => e.Time)"
                    YValueSelector="@(e => (decimal?)e.TIM_GPU_UserCnt2)"
                    ChartType="SeriesType.Line"
                    ModalId="gpu_user_count2_line"
                    WorksheetName="人次統計"
                    OnDataLoaded="@HandleGPUUser2DataLoaded" />
                </div>

            </div>

            <!--Excel表格-->
            <div class="row row-cols-1 row-cols-xl-2 g-4 mb-4">
                <div class="col">
                    @if (PCRoomExcel == null)
                    {
                        <p>正在載入資料...</p>
                    }
                    else
                    {
                        <MyCard_dark2 Title="電腦教室使用與管理" IsModal="false">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>

                                        @foreach (DataColumn col in PCRoomExcel.Columns)
                                        {
                                            <th>
                                                <span class="badge bg-info fs-6 rounded-pill">@col.ColumnName</span>
                                            </th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (DataRow row in PCRoomExcel.Rows)
                                    {
                                        <tr>
                                            @foreach (DataColumn col in PCRoomExcel.Columns)
                                            {
                                                <td>@row[col.ColumnName]</td>
                                            }
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </MyCard_dark2>
                    }
                </div>

                <div>
                    <!--堆疊柱狀-->
                    <ExcelChart TItem="TIM_CenterMoney"
                                Title="本校資訊預算統計資料"
                                ExcelUrl="@Xlsx_CenterMoney"
                                TableData="@centerMoneyTableData"
                                TableColumns="@centerMoneyTableColumns"
                                ChartData="@centerMoneyChartData"
                                ChartOptions="@centerMoneyChartOptions"
                                SeriesName="資訊預算統計"
                                XValueSelector="@(e => e.Time)"
                                YValueSelector="@(e => (decimal?)e.TIM_HardwareCost)"
                                ChartType="SeriesType.Bar"
                                ModalId="tim_CenterMoney_bar"
                                MultiSeries="true"
                                OnDataLoaded="@HandleCenterMoneyDataLoaded" />
                </div>
            </div>
            
            <MyAlert Text="資訊中心 | 網路組"></MyAlert>
            <div class="row row-cols-1 g-4 mb-4">
                <!-- 個人資料保護 -->
                <div class="col">
                    <Internet></Internet>
                </div>
            </div>
        </div>
    </div>

    <!-- 其他圖表 -->
    @* <div class="row row-cols-1 row-cols-lg-2 g-4 mb-4">
        <div class="col">
            <AzureChart TContainer="UA_LibModel"
            TItem="UA_LibData"
            ContainerType="@ContainerType.UA_Lib"
            CosmosDataPropertyName="UA_LibData"
            Title="中文藏書種類(Azure 新版方式)"
            ChartOptions="@treemapChartOptions"
            ChartType="SeriesType.Treemap"
            SeriesName="中文藏書種類"
            XValueSelector="@(e => e.SchoolName)"
            YValueSelector="@(e => (decimal?)e.Lib_AV)"
            ModalId="Lib_Chn_type"
            PreChartData="@pre_UALibData"
            TargetSchoolName="@filterYuntech"
            @bind-TreemapConfig="treemapConfig"
            OnDataLoaded="@HandleTreemapChartDataLoaded" />
        </div>
    </div> *@
}

@code {
    private bool isLoading = true;
    private string? errorMessage;

    // 預載入的資料
    private IEnumerable<Res_CombineMoneyModel>? pre_ResCombineMoneyData;
    private IEnumerable<UA_LibModel>? pre_UALibData;
    private List<Dictionary<string, object>>? filteredTotalFundingTableData;
    private List<Dictionary<string, object>>? filteredLibChTotalTableData;
    private List<Dictionary<string, object>>? filteredGovFundingTableData;

    // 圖表選項
    private ApexChartOptions<Res_CombineMoney>? totalFundingChartOptions;
    private ApexChartOptions<Res_CombineMoney>? govFundingChartOptions;
    private ApexChartOptions<Res_CombineMoney>? enterpriseFundingChartOptions;
    private ApexChartOptions<Res_CombineMoney>? avgTeacherFundingChartOptions;

    private ApexChartOptions<UA_LibData>? UA_LibModelOpt;
    private ApexChartOptions<UA_LibData>? treemapChartOptions;
    private ApexChartOptions<UA_LibData>? Lib_Ch_AllType_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_En_Total_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_BoundJournals_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_Paper_ChJP_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_Paper_En_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_E_Journals_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_E_DB_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_E_Book_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_AV_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_Paper_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_rs_Total_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_ChEn_PieChartOpt;
    private ApexChartOptions<UA_LibData>? Lib_Usr_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_Lend_YunTechOpt;
    private ApexChartOptions<UA_LibData>? Lib_OnlineUse_YunTechOpt;

    // 圓餅圖欄位配置 - 使用 nameof 避免硬編碼字串
    private Dictionary<string, string> libPieChartFields;

    // Treemap 配置
    private TreemapConfiguration? treemapConfig;


    // 顯示的學校
    private string filterYuntech = "雲科";

    // 系統組三大需求表相關變數
    private List<Dictionary<string, object>> tcsFormTableData;
    private List<string> tcsFormTableColumns;
    // 系統組三大需求表 (橫式長條圖)
    private List<UA_LibExcel_Money> tcsFormChartData;
    private ApexChartOptions<UA_LibExcel_Money> tcsFormChartOptions;

    // 註冊學生人數圖表變數
    private List<UA_LibExcel_Money> studCountChartData;
    private ApexChartOptions<UA_LibExcel_Money> studCountChartOptions;

    // 學生平均享有圖資經費圖表變數
    private List<UA_LibExcel_Money> studPerMoneyChartData;
    private ApexChartOptions<UA_LibExcel_Money> studPerMoneyChartOptions;

    private string Xlsx_TCSForm = "http://140.125.240.94/grafana/雲科大資料/圖書館-生均使用圖書資源經費.xlsx";

    // 專業館員人力指標相關變數
    private List<Dictionary<string, object>> workerTableData;
    private List<string> workerTableColumns;
    private List<UA_LibExcel_Worker> workerChartData;
    private ApexChartOptions<UA_LibExcel_Worker> workerChartOptions;
    private string Xlsx_Worker = "http://140.125.240.94/grafana/雲科大資料/圖書館-專業館員人力指標.xlsx";

    // 新增：系統登入次數相關變數
    private List<Dictionary<string, object>> loginTableData;
    private List<string> loginTableColumns;
    private List<TIS_SysLoginCnt> loginChartData;
    private ApexChartOptions<TIS_SysLoginCnt> loginChartOptions;
    private string Xlsx_Login = "http://140.125.240.94/grafana/雲科大資料/系統組-202505系統登入次數.xlsx";

    // 新增：系統開發數相關變數
    private List<Dictionary<string, object>> sysCountTableData;
    private List<string> sysCountTableColumns;
    private List<TIS_SysDevCnt> sysCountChartData;
    private ApexChartOptions<TIS_SysDevCnt> sysCountChartOptions;
    private string Xlsx_SysCount = "http://140.125.240.94/grafana/雲科大資料/系統組-自行開發系統數.xlsx";

    // 新增：系統組三大需求表相關變數 (重命名以避免衝突)
    private List<Dictionary<string, object>> tcsFormTableDataNew;
    private List<string> tcsFormTableColumnsNew;
    private List<TIS_Form> tcsFormChartDataNew;
    private ApexChartOptions<TIS_Form> tcsFormChartOptionsNew;
    private string Xlsx_TCSFormNew = "http://140.125.240.94/grafana/雲科大資料/系統組-三大需求表.xlsx";

    // 新增：TCS經費相關變數
    private List<Dictionary<string, object>> tcsMoneyTableData;
    private List<string> tcsMoneyTableColumns;
    private List<TIM_CenterMoney2> tcsMoneyChartData;
    private ApexChartOptions<TIM_CenterMoney2> tcsMoneyChartOptions;
    private string Xlsx_TCSMoney = "http://140.125.240.94/grafana/雲科大資料/資訊中心歷年經費統計表.xlsx";

    // 新增：GPU使用人數相關變數
    private List<Dictionary<string, object>> gpuUserTableData;
    private List<string> gpuUserTableColumns;
    private List<TIM_GPUser> gpuUserChartData;
    private ApexChartOptions<TIM_GPUser> gpuUserChartOptions;
    private Dictionary<string, SeriesType> gpuUserChartSeriesTypes = new Dictionary<string, SeriesType>();
    private string Xlsx_GPUser = "http://140.125.240.94/grafana/雲科大資料/應用組-GPU使用人數.xlsx";

    // 新增：GPU使用人次統計相關變數
    private List<Dictionary<string, object>> gpuUser2TableData;
    private List<string> gpuUser2TableColumns;
    private List<TIM_GPUser> gpuUser2ChartData;
    private ApexChartOptions<TIM_GPUser> gpuUser2ChartOptions;

    // 新增：本校資訊預算統計資料相關變數
    private List<Dictionary<string, object>> centerMoneyTableData;
    private List<string> centerMoneyTableColumns;
    private List<TIM_CenterMoney> centerMoneyChartData;
    private ApexChartOptions<TIM_CenterMoney> centerMoneyChartOptions;

    // 新增：GPU使用率相關變數
    private List<Dictionary<string, object>> gpuRateTableData;
    private List<string> gpuRateTableColumns;
    private List<TIM_GPURate> gpuRateChartData;
    private ApexChartOptions<TIM_GPURate> gpuRateChartOptions;
    private string Xlsx_GPURate = "http://140.125.240.94/grafana/雲科大資料/應用組-GPU使用率.xlsx";
    private string Xlsx_PCRoomManage = "http://140.125.240.94/grafana/雲科大資料/應用組-電腦教室硬體配置總表.xlsx";
    private string Xlsx_CenterMoney = "http://140.125.240.94/grafana/雲科大資料/應用組-本校資訊預算統計資料.xlsx";

    private DataTable PCRoomExcel;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // 初始化圖表選項
            InitializeChartOptions();

            // 預載入共用的資料源
            await PreloadSharedData();
            filterData();

            // 應用組-電腦教室使用與管理(表格呈現)
            PCRoomExcel = await ExcelService.GetExcelDataFromUrlAsync(Xlsx_PCRoomManage);

            isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"初始化錯誤: {ex.Message}";
            isLoading = false;
        }
    }

    // 預載入共用的資料源(Azure)
    private async Task PreloadSharedData()
    {
        try
        {
            // 載入 Res_CombineMoney 資料 (用於前4個圖表)
            pre_ResCombineMoneyData = await _cosmosService.GetItemsAsync<Res_CombineMoneyModel>(
                ContainerType.Res_CombineMoney, "SELECT * FROM c");

            // 載入 UA_Lib 資料 (用於視聽資料圖表)
            pre_UALibData = await _cosmosService.GetItemsAsync<UA_LibModel>(
                ContainerType.UA_Lib, "SELECT * FROM c");
        }
        catch (Exception ex)
        {
            errorMessage = $"預載入資料錯誤: {ex.Message}";
            throw;
        }
    }

    private void filterData()
    {
        // 篩選後的表格資料
        filteredLibChTotalTableData = _chartDataService.CreateFilteredTableData<UA_LibModel, UA_LibData>(
            pre_UALibData,
            container => container.UA_LibData,
            item => item.SchoolName,
            filterYuntech
        );

        filteredTotalFundingTableData = _chartDataService.CreateFilteredTableData<Res_CombineMoneyModel, Res_CombineMoney>(
            pre_ResCombineMoneyData,
            container => container.Res_CombineMoneyData,
            item => item.SchoolName,
            filterYuntech
        );

        filteredGovFundingTableData = _chartDataService.CreateFilteredTableData<Res_CombineMoneyModel, Res_CombineMoney>(
            pre_ResCombineMoneyData,
            container => container.Res_CombineMoneyData,
            item => item.SchoolName,
            filterYuntech
        );
    }


    private void InitializeChartOptions()
    {
        // 總計經費圖表選項（藍色）
        totalFundingChartOptions = _chartDataService.CreateBarChartOptions<Res_CombineMoney>(
            new List<string> { "#1A73E8" },
            showDataLabels: true);

        // 政府資助圖表選項（綠色）
        govFundingChartOptions = _chartDataService.CreateBarChartOptions<Res_CombineMoney>(
            new List<string> { "#34A853" },
            showDataLabels: true);

        // 企業資助圖表選項（橙色）
        enterpriseFundingChartOptions = _chartDataService.CreateBarChartOptions<Res_CombineMoney>(
            new List<string> { "#FF9800" },
            showDataLabels: true);

        // 平均每師經費圖表選項（紫色）
        avgTeacherFundingChartOptions = _chartDataService.CreateBarChartOptions<Res_CombineMoney>(
            new List<string> { "#9C27B0" },
            showDataLabels: true);


        UA_LibModelOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(
            new List<string> { "#9C27B0" },
            showDataLabels: true);

        // Treemap圖表選項
        var treemapColors = new List<string>();
        for (int i = 0; i < 5; i++)
        {
            treemapColors.AddRange(_chartDataService.GetStandardColors(3, i));
        }
        treemapChartOptions = _chartDataService.CreateTreemapChartOptions<UA_LibData>(
            treemapColors,
            showDataLabels: true);

        // 修改Treemap選項，使其更適合顯示單一學校的多個項目
        treemapChartOptions.PlotOptions.Treemap.Distributed = true;
        treemapChartOptions.PlotOptions.Treemap.EnableShades = true;
        treemapChartOptions.PlotOptions.Treemap.ShadeIntensity = 0.4f;

        // 紙本圖書 (藍色系)
        Lib_Ch_AllType_YunTechOpt = _chartDataService.CreateAreaChartOptions<UA_LibData>(
           new List<string> { "#008FFB" });
        Lib_En_Total_YunTechOpt = _chartDataService.CreateAreaChartOptions<UA_LibData>(
            new List<string> { "#1A73E8" });

        // 期刊 (綠色系)
        Lib_BoundJournals_YunTechOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(
            new List<string> { "#00E396" });
        Lib_Paper_ChJP_YunTechOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(
            new List<string> { "#34A853" });
        Lib_Paper_En_YunTechOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(
            new List<string> { "#26a69a" });

        // E化 (紫色系)
        Lib_E_Journals_YunTechOpt = _chartDataService.CreateAreaChartOptions<UA_LibData>(
            new List<string> { "#775DD0" });
        Lib_E_DB_YunTechOpt = _chartDataService.CreateAreaChartOptions<UA_LibData>(
            new List<string> { "#9C27B0" });
        Lib_E_Book_YunTechOpt = _chartDataService.CreateAreaChartOptions<UA_LibData>(
            new List<string> { "#D10CE8" });

        // 非書資料 (橙色系)
        Lib_AV_YunTechOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(
            new List<string> { "#FEB019" });
        Lib_Paper_YunTechOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(
            new List<string> { "#FF9800" });

        // 圓餅圖
        Lib_rs_Total_YunTechOpt = _chartDataService.CreatePieChartOptions<UA_LibData>(
                new List<string> { "#f48a54", "#f4da54", "#FF9800" });

        // 中英文藏書比例圓餅圖
        Lib_ChEn_PieChartOpt = _chartDataService.CreatePieChartOptions<UA_LibData>(
                new List<string> { "#008FFB", "#00E396", "#FEB019" });

        libPieChartFields = new Dictionary<string, string>
        {
            { "圖書資源", nameof(UA_LibData.Lib_TotalBookRs) },
            { "電子資源", nameof(UA_LibData.Lib_TotalECRs) },
            { "視聽資源", nameof(UA_LibData.Lib_TotalAVRs) }
        };

        // 讀者服務指標
        Lib_Usr_YunTechOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(new List<string> { "#64B5F6" });
        Lib_Lend_YunTechOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(new List<string> { "#81C784" });
        Lib_OnlineUse_YunTechOpt = _chartDataService.CreateBarChartOptions<UA_LibData>(new List<string> { "#CE93D8" });

        tcsFormChartOptions = _chartDataService.CreateAreaChartOptions<UA_LibExcel_Money>(
        new List<string> { "#00E396" });
        
        // 註冊學生人數圖表選項（藍色）
        studCountChartOptions = _chartDataService.CreateAreaChartOptions<UA_LibExcel_Money>(
        new List<string> { "#1A73E8" });
        
        // 學生平均享有圖資經費圖表選項（橙色）
        studPerMoneyChartOptions = _chartDataService.CreateAreaChartOptions<UA_LibExcel_Money>(
        new List<string> { "#FF9800" });

        workerChartOptions = _chartDataService.CreateAreaChartOptions<UA_LibExcel_Worker>(
                new List<string> { },
                isSpline: true,
                isStacked: false);

        // 新增：系統登入次數 (Treemap)
        var loginColors = new List<string>();
        for (int i = 0; i < 5; i++)
        {
            loginColors.AddRange(_chartDataService.GetStandardColors(5, i));
        }
        loginChartOptions = _chartDataService.CreateTreemapChartOptions<TIS_SysLoginCnt>(
            loginColors,
            showDataLabels: true);

        // 新增：系統開發數 (面積圖)
        var sysCountColors = _chartDataService.GetStandardColors(1, 4);
        sysCountChartOptions = _chartDataService.CreateAreaChartOptions<TIS_SysDevCnt>(
            sysCountColors,
            showDataLabels: true,
            isSpline: true,
            isStacked: false);

        // 新增：系統組三大需求表 (橫式長條圖)
        var tcsFormColors = _chartDataService.GetStandardColors(3, 3);
        tcsFormChartOptionsNew = _chartDataService.CreateBarChartOptions<TIS_Form>(
            new List<string> { "#00E396" },
            isHorizontal: false,
            showDataLabels: true,
            isStacked: false);

        // 新增：TCS經費 (面積圖)
        var tcsMoneyColors = _chartDataService.GetStandardColors(3, 1);
        tcsMoneyChartOptions = _chartDataService.CreateBarChartOptions<TIM_CenterMoney2>(
            tcsMoneyColors,
            showDataLabels: true);

        // 新增：GPU使用人數 (混合圖表)
        var gpuUserColors = _chartDataService.GetStandardColors(2, 2);
        gpuUserChartOptions = _chartDataService.CreateMixedChartOptions<TIM_GPUser>(
            gpuUserColors,
            showDataLabels: true,
            isMarkers: false);

        // 隱藏折線圖的標記點（圓點）
        gpuUserChartOptions.Markers = new Markers
        {
            Size = 0, // 設置為0表示不顯示標記點
        };

        // 新增：GPU使用人次統計 (折線圖)
        var gpuUser2Colors = _chartDataService.GetStandardColors(1, 3);
        gpuUser2ChartOptions = _chartDataService.CreateLineChartOptions<TIM_GPUser>(
            new List<string> { "#008FFB" },
            showDataLabels: false,
            isSpline: true,
            isMarkers: true);

        // 新增：本校資訊預算統計資料 (堆疊柱狀圖)
        var centerMoneyColors = _chartDataService.GetStandardColorsAcrossGroups(10, 0);
        centerMoneyChartOptions = _chartDataService.CreateBarChartOptions<TIM_CenterMoney>(
            centerMoneyColors,
            showDataLabels: true,
            isStacked: true);

        // 新增：GPU使用率 (折線圖)
        var gpuRateColors = _chartDataService.GetStandardColors(1, 0);
        gpuRateChartOptions = _chartDataService.CreateLineChartOptions<TIM_GPURate>(
            gpuRateColors,
            showDataLabels: false,
            isSpline: true);
    }


    // 處理總計經費數據加載完成
    private async Task HandleTotalFundingDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            // 這裡可以添加額外的數據處理邏輯
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理總計經費數據出錯: {ex.Message}";
        }
    }

    // 處理政府資助數據加載完成
    private async Task HandleGovFundingDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理政府資助數據出錯: {ex.Message}";
        }
    }

    // 處理企業資助數據加載完成
    private async Task HandleEnterpriseFundingDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理企業資助數據出錯: {ex.Message}";
        }
    }

    // 處理平均每師經費數據加載完成
    private async Task HandleAvgTeacherFundingDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理平均每師經費數據出錯: {ex.Message}";
        }
    }

    // 處理視聽資料數據加載完成
    private async Task HandleLibAVDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理視聽資料數據出錯: {ex.Message}";
        }
    }

    // 中文藏書量折線區域圖
    private async Task Lib_ChartLoad(List<Dictionary<string, object>> data)
    {
        try
        {
            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"{ex.Message}";
        }
    }


    // 處理Treemap圖數據加載完成
    private async Task HandleTreemapChartDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            // 建立 Treemap 配置，使用生成的數據
            treemapConfig = new TreemapConfiguration
                {
                    TargetSchoolName = filterYuntech,
                    DisplayFieldName = "Lib_AV",
                    ColumnTypes = new[] {
                    ChartColumnType.Lib_Ch_Philosophy,
                    ChartColumnType.Lib_Ch_Religion,
                    ChartColumnType.Lib_Ch_Science,
                    ChartColumnType.Lib_Ch_Applied,
                    ChartColumnType.Lib_Ch_Social,
                    ChartColumnType.Lib_Ch_History,
                    ChartColumnType.Lib_Ch_Language,
                    ChartColumnType.Lib_Ch_Art
                },
                    GetDisplayNameFunc = (columnType) =>
                    {
                        // 使用 ChartConfigurationService 來獲取顯示名稱，並去掉 "中文紙本圖書-" 前綴
                        var attr = _configService.GetChartColumnAttribute<UA_LibData>(columnType);
                        var displayName = attr?.DisplayName ?? columnType.ToString();

                        // 去掉 "中文紙本圖書-" 前綴
                        if (displayName.StartsWith("中文紙本圖書-"))
                        {
                            displayName = displayName.Substring("中文紙本圖書-".Length);
                        }

                        return displayName;
                    },
                    GetPropertyNameFunc = (columnType) =>
                    {
                        // 使用 ChartConfigurationService 來獲取屬性名稱
                        return _configService.GetPropertyNameForColumnType<UA_LibData>(columnType);
                    }
                };

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理Treemap圖數據出錯: {ex.Message}";
        }
    }

    // 處理系統組三大需求表數據加載完成
    private async Task HandleTCSFormDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            tcsFormTableData = data;
            tcsFormTableColumns = data.First().Keys.ToList();

            // 使用新的配置服務處理數據
            var settings = _configService.CreateSettingsFromModel<UA_LibExcel_Money>("年度");
            var valueColumns = settings.GetChartValueColumns();

            // 轉換基礎數據模型
            var baseChartData = _chartDataService.ConvertToModel<UA_LibExcel_Money>(
                tcsFormTableData,
                settings.CategoryColumn,
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "Year");

            // 為每個圖表創建獨立的數據列表，並按對應的 Y 軸值排序
            tcsFormChartData = baseChartData
                .OrderBy(d =>d.Year)
                .ToList();
                
            studCountChartData = baseChartData
                .OrderBy(d => d.Year)
                .ToList();
                
            studPerMoneyChartData = baseChartData
                .OrderBy(d => d.Year)
                .ToList();

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理系統組三大需求表數據出錯: {ex.Message}";
        }
    }

    // 處理專業館員人力指標數據加載完成
    private async Task HandleWorkerDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            workerTableData = data;
            workerTableColumns = data.First().Keys.ToList();

            // 使用新的配置服務處理數據
            var settings = _configService.CreateSettingsFromModel<UA_LibExcel_Worker>("年度");
            var valueColumns = settings.GetChartValueColumns();

            // 取得專業館員人力指標數據
            workerChartData = _chartDataService.ConvertToModel<UA_LibExcel_Worker>(
                workerTableData,
                settings.CategoryColumn,
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "Year")
                .OrderBy(d => d.Year)
                .ToList();

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理專業館員人力指標數據出錯: {ex.Message}";
        }
    }

    // 新增：處理系統登入次數數據加載完成
    private async Task HandleLoginDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            loginTableData = data;
            loginTableColumns = data.First().Keys.ToList();

            // 使用新的配置服務處理數據
            var settings = _configService.CreateSettingsFromModel<TIS_SysLoginCnt>("REQ_APPID");
            var valueColumns = settings.GetChartValueColumns();

            // 取得系統登入次數數據
            loginChartData = _chartDataService.ConvertToModel<TIS_SysLoginCnt>(
                loginTableData,
                settings.CategoryColumn,
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "REQ_APPID")
                .OrderByDescending(d => d.TIS_LoginCnt)
                .Take(15) // 只顯示前15個系統，避免圖表太擠
                .ToList();

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理系統登入次數數據出錯: {ex.Message}";
        }
    }

    // 新增：處理開發系統數量數據加載完成
    private async Task HandleSysCountDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            sysCountTableData = data;
            sysCountTableColumns = data.First().Keys.ToList();

            // 使用新的配置服務處理數據
            var settings = _configService.CreateSettingsFromModel<TIS_SysDevCnt>("時間");
            var valueColumns = settings.GetChartValueColumns();

            // 取得系統開發數數據
            sysCountChartData = _chartDataService.ConvertToModel<TIS_SysDevCnt>(
                sysCountTableData,
                settings.CategoryColumn,
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "Time")
                .OrderBy(d => _configService.GetTimeOrderKey(d.Time))
                .ToList();

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理系統開發數數據出錯: {ex.Message}";
        }
    }

    // 新增：處理系統組三大需求表數據加載完成 (重命名以避免衝突)
    private async Task HandleTCSFormNewDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            tcsFormTableDataNew = data;
            tcsFormTableColumnsNew = data.First().Keys.ToList();

            // 使用新的配置服務處理數據
            var settings = _configService.CreateSettingsFromModel<TIS_Form>("表單類型");
            var valueColumns = settings.GetChartValueColumns();

            // 取得系統組需求表數據
            tcsFormChartDataNew = _chartDataService.ConvertToModel<TIS_Form>(
                tcsFormTableDataNew,
                settings.CategoryColumn,
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "FormType")
                .OrderByDescending(d => d.TCS_FormCnt)
                .ToList();

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理系統組三大需求表數據出錯: {ex.Message}";
        }
    }

    // 新增：處理TCS經費數據加載完成
    private async Task HandleTCSMoneyDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            tcsMoneyTableData = data;
            tcsMoneyTableColumns = data.First().Keys.ToList();

            // 使用 CreateFilteredSettings 只顯示業務費和設備費
            var settings = _configService.CreateFilteredSettingsWithCategory<TIM_CenterMoney2>(
                "年度",
                ChartColumnType.TIM_BusinessCost,
                ChartColumnType.TIM_EquipmentCost);
            var valueColumns = settings.GetChartValueColumns();

            // 取得TCS經費數據
            tcsMoneyChartData = _chartDataService.ConvertToModel<TIM_CenterMoney2>(
                tcsMoneyTableData,
                settings.CategoryColumn,
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "Time")
                .OrderBy(d => _configService.GetTimeOrderKey(d.Time))
                .ToList();

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理TCS經費數據出錯: {ex.Message}";
        }
    }

    // 新增：處理GPU使用人數數據加載完成
    private async Task HandleGPUUserDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            gpuUserTableData = data;
            gpuUserTableColumns = data.First().Keys.ToList();

            // 使用新的配置服務處理數據
            var settings = _configService.CreateFilteredSettings<TIM_GPUser>(ChartColumnType.TIM_GPU_UserCnt);
            var valueColumns = settings.GetChartValueColumns();

            // 取得GPU使用人數數據並按時間排序
            var tempData = _chartDataService.ConvertToModel<TIM_GPUser>(
                gpuUserTableData,
                "時間",
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "Time")
                .OrderBy(d => _configService.GetTimeOrderKey(d.Time))
                .ToList();

            // 計算累計數量
            int runningTotal = 0;
            gpuUserChartData = new List<TIM_GPUser>();

            foreach (var item in tempData)
            {
                // 累加計算
                runningTotal += (int)item.TIM_GPU_UserCnt;

                // 創建一個新的對象，同時包含原始值和累計值
                gpuUserChartData.Add(new TIM_GPUser
                {
                    Time = item.Time,
                    TIM_GPU_UserCnt = item.TIM_GPU_UserCnt,
                    AccumulatedUserCount = runningTotal,
                    ValueColumns = new Dictionary<string, decimal>
                    {
                        { "單期人數", item.TIM_GPU_UserCnt },
                        { "累計人數", runningTotal }
                    }
                });
            }

            // 設置每個系列的圖表類型
            gpuUserChartSeriesTypes.Clear();
            gpuUserChartSeriesTypes.Add("單期人數", SeriesType.Bar);
            gpuUserChartSeriesTypes.Add("累計人數", SeriesType.Line);

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理GPU使用人數數據出錯: {ex.Message}";
        }
    }

    // 新增：處理GPU使用人次統計數據加載完成
    private async Task HandleGPUUser2DataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            gpuUser2TableData = data;
            gpuUser2TableColumns = data.First().Keys.ToList();

            // 使用新的配置服務處理數據
            var settings = _configService.CreateFilteredSettings<TIM_GPUser>(ChartColumnType.TIM_GPU_UserCnt2);
            var valueColumns = settings.GetChartValueColumns();

            // 取得GPU使用人次統計數據並按時間排序
            gpuUser2ChartData = _chartDataService.ConvertToModel<TIM_GPUser>(
                gpuUser2TableData,
                "時間",
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "Time")
                .OrderBy(d => _configService.GetTimeOrderKey(d.Time))
                .ToList();

            // 確保 ValueColumns 字典包含正確的數據
            foreach (var item in gpuUser2ChartData)
            {
                item.ValueColumns["GPU使用人次"] = item.TIM_GPU_UserCnt2;
            }

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理GPU使用人次統計數據出錯: {ex.Message}";
        }
    }

    // 新增：處理GPU使用率數據加載完成
    private async Task HandleGPURateDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            gpuRateTableData = data;
            gpuRateTableColumns = data.First().Keys.ToList();

            // 使用新的配置服務處理數據
            var settings = _configService.CreateFilteredSettings<TIM_GPURate>(ChartColumnType.TIM_GPURateP);
            var valueColumns = settings.GetChartValueColumns();


            // 取得GPU使用率數據
            gpuRateChartData = _chartDataService.ConvertToModel<TIM_GPURate>(
                gpuRateTableData,
                "時間",
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "Time")
                .OrderBy(d => _configService.GetTimeOrderKey(d.Time))
                .ToList();

            // 確保 ValueColumns 字典包含正確的數據
            foreach (var item in gpuRateChartData)
            {
                item.ValueColumns["GPU使用率"] = item.TIM_GPURateP;
            }

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理GPU使用率數據出錯: {ex.Message}";
        }
    }

    // 新增：處理本校資訊預算統計資料數據加載完成
    private async Task HandleCenterMoneyDataLoaded(List<Dictionary<string, object>> data)
    {
        try
        {
            // 過濾掉所有欄位都為空的資料行
            var filteredData = data.Where(row =>
            {
                // 檢查是否所有欄位的值都為空或null
                return row.Values.Any(value => 
                    value != null && 
                    !string.IsNullOrWhiteSpace(value.ToString()));
            }).ToList();

            centerMoneyTableData = filteredData;
            centerMoneyTableColumns = filteredData.First().Keys.ToList();

            // 使用 CreateExcludedSettings 顯示所有預算項目（排除合計欄位）
            var settings = _configService.CreateExcludedSettings<TIM_CenterMoney>(
                "年度",
                ChartColumnType.TIM_PCRoomTotal);
            var valueColumns = settings.GetChartValueColumns();

            // 取得本校資訊預算統計數據
            centerMoneyChartData = _chartDataService.ConvertToModel<TIM_CenterMoney>(
                centerMoneyTableData,
                settings.CategoryColumn,
                valueColumns,
                settings.DisplayNames,
                markTopItem: true,
                keyPropertyName: "Time")
                .OrderBy(d => _configService.GetTimeOrderKey(d.Time))
                .ToList();

            await InvokeAsync(StateHasChanged);
        }
        catch (Exception ex)
        {
            errorMessage = $"處理本校資訊預算統計數據出錯: {ex.Message}";
        }
    }
}
