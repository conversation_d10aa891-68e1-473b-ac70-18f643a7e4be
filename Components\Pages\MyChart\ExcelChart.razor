@using ApexCharts
@using System.Linq
@using System.Collections.Generic
@using System.Data

@typeparam TItem where TItem : class


<MyCard_dark Year="@Year" Title="@Title" IsModal="true" TableColumns="@TableColumns" TableData="@TableData" ExcelUrl="@ExcelUrl" ModalId="@ModalId"
              EnableClickMeButton="@EnableClickMeButton" ClickMeContent="@ClickMeModalContent">
    @if (isLoading)
    {
        <div class="text-center mt-5">
            <div class="spinner-border mt-5" role="status">
            </div>
        </div>
    }
    else 
    {
        <ApexChart TItem="TItem"
        Options="ChartOptions">
            @if (!MultiSeries)
            {
                @if (ChartType == SeriesType.RadialBar && typeof(TItem).GetProperty("PercentSmallMoney") != null)
                {
                    @if (isShowDataLabels)
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@SeriesName"
                        Items="@ChartData"
                        XValue="@XValueSelector"
                        YValue="@(item => {
                                             var propInfo = typeof(TItem).GetProperty("PercentSmallMoney");
                                             if (propInfo != null)
                                                 return (decimal?)propInfo.GetValue(item);
                                             return YValueSelector(item);
                                         })"
                        SeriesType="@ChartType"
                        ShowDataLabels />
                    }
                    else
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@SeriesName"
                        Items="@ChartData"
                        XValue="@XValueSelector"
                        YValue="@(item => {
                                             var propInfo = typeof(TItem).GetProperty("PercentSmallMoney");
                                             if (propInfo != null)
                                                 return (decimal?)propInfo.GetValue(item);
                                             return YValueSelector(item);
                                         })"
                        SeriesType="@ChartType" />
                    }
                }
                else
                {
                    @if (isShowDataLabels)
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@SeriesName"
                        Items="@ChartData"
                        XValue="@XValueSelector"
                        YValue="@YValueSelector"
                        SeriesType="@ChartType"
                        ShowDataLabels />
                    }
                    else
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@SeriesName"
                        Items="@ChartData"
                        XValue="@XValueSelector"
                        YValue="@YValueSelector"
                        SeriesType="@ChartType" />
                    }
                }
            }
            else
            {
                var ySelector = CreateYValueSelector();
                foreach (var prop in GetValueProperties())
                {
                    // 獲取針對此屬性的圖表類型
                    var seriesType = GetSeriesType(prop.PropName);

                    @if (isShowDataLabels)
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@prop.DisplayName"
                        Items="@ChartData"
                        XValue="@XValueSelector"
                        YValue="@(item => {
                                             var value = ySelector(item, prop.PropName);
                                             return value;
                                         })"
                        SeriesType="@seriesType"
                        ShowDataLabels />
                    }
                    else
                    {
                        <ApexPointSeries TItem="TItem"
                        Name="@prop.DisplayName"
                        Items="@ChartData"
                        XValue="@XValueSelector"
                        YValue="@(item => {
                                             var value = ySelector(item, prop.PropName);
                                             return value;
                                         })"
                        SeriesType="@seriesType" />
                    }
                }
            }
        </ApexChart>
    }
</MyCard_dark>

@code {
    [Parameter] public string Title { get; set; } = "數據圖表";
    [Parameter] public string? Year { get; set; }
    [Parameter] public string ExcelUrl { get; set; }
    [Parameter] public List<Dictionary<string, object>>? TableData { get; set; }
    [Parameter] public List<string>? TableColumns { get; set; }
    [Parameter] public List<TItem> ChartData { get; set; }
    [Parameter] public ApexChartOptions<TItem> ChartOptions { get; set; }
    [Parameter] public SeriesType ChartType { get; set; } = SeriesType.Bar;
    [Parameter] public string SeriesName { get; set; } = "數據";
    [Parameter] public Func<TItem, object> XValueSelector { get; set; }
    [Parameter] public Func<TItem, decimal?> YValueSelector { get; set; }
    [Parameter] public EventCallback<List<Dictionary<string, object>>> OnDataLoaded { get; set; }
    [Parameter] public string ModalId { get; set; } = "chartModal";
    [Parameter] public bool MultiSeries { get; set; } = false;
    [Parameter] public bool isShowDataLabels { get; set; } = true;
    [Parameter] public bool IsMixedChart { get; set; } = false;
    [Parameter] public Dictionary<string, SeriesType> SeriesTypes { get; set; } = new Dictionary<string, SeriesType>();

    [Parameter] public string CategoryField { get; set; }
    [Parameter] public string ValueField { get; set; }

    // 新增參數，用於指定工作表索引或名稱
    [Parameter] public int? WorksheetIndex { get; set; } // 若有指定，優先使用索引
    [Parameter] public string? WorksheetName { get; set; } // 若無索引但有名稱，則使用名稱

    // 新增："按我唷"按鈕功能參數
    [Parameter] public bool EnableClickMeButton { get; set; } = false;

    private bool isLoading = true;
    private string errorMessage;

    private string GetDisplayName(string propertyName)
    {
        // 針對常見屬性名稱返回友好顯示名
        switch (propertyName)
        {
            case "SmallMoney": return "小計金額";
            case "GovMoney": return "政府部門資助";
            case "CompanyMoney": return "企業部門資助";
            case "NonProfitMoney": return "非營利機構資助";
            case "TeacherCount": return "專任教師數";
            case "StudentCount": return "學生人數";
            default: return propertyName;
        }
    }

    // 在元件參數設置後立即檢查數據狀態
    protected override async void OnParametersSet()
    {
        // 如果已經有圖表數據，立即設置為非加載狀態
        if (ChartData != null && ChartData.Any())
        {

            isLoading = false;
            StateHasChanged(); // 強制 UI 立即更新
        }
    }

    protected override async Task OnInitializedAsync()
    {
        // 立即檢查是否已有數據或無需加載
        if (string.IsNullOrEmpty(ExcelUrl) || (ChartData != null && ChartData.Any()))
        {
            isLoading = false; 
            return;
        }

        // 通知 UI 顯示加載動畫
        await InvokeAsync(StateHasChanged);

        // 有需要從 Excel 加載數據時，保持 isLoading = true
        try
        {
            DataTable dataTable;
            
            // 根據參數選擇適當的方法獲取工作表資料
            if (WorksheetIndex.HasValue)
            {
                // 使用索引獲取指定工作表
                dataTable = await _excelService.GetExcelDataFromUrlAsync(ExcelUrl, WorksheetIndex.Value);
            }
            else if (!string.IsNullOrEmpty(WorksheetName))
            {
                // 使用名稱獲取指定工作表
                dataTable = await _excelService.GetExcelDataFromUrlAsync(ExcelUrl, WorksheetName);
            }
            else
            {
                // 預設獲取第一個工作表
                dataTable = await _excelService.GetExcelDataFromUrlAsync(ExcelUrl);
            }
            
            if (dataTable == null)
            {
                errorMessage = "無法從URL獲取Excel數據";
                isLoading = false;
                StateHasChanged(); // 更新 UI 顯示錯誤狀態
                return;
            }

            var result = _excelService.ConvertDataTableToPageFormat(dataTable);
            if (result.TableData == null)
            {
                errorMessage = "轉換Excel數據失敗";
                isLoading = false;
                StateHasChanged(); // 更新 UI 顯示錯誤狀態
                return;
            }

            TableData = result.TableData;
            TableColumns = result.TableColumns;

            // 通知父元件數據已加載
            if (OnDataLoaded.HasDelegate)
            {
                await OnDataLoaded.InvokeAsync(TableData);
            }

            isLoading = false;
            // 完成加載後更新 UI
            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = $"數據載入錯誤: {ex.Message}";
            isLoading = false;
            // 錯誤情況下更新 UI
            StateHasChanged();
        }
    }


    // 抽出屬性讀取方法
    private List<(string PropName, string DisplayName)> GetValueProperties()
    {
        var result = new List<(string PropName, string DisplayName)>();

        var firstItem = ChartData.FirstOrDefault();
        if (firstItem == null) return result;

        var valueColumnsProperty = typeof(TItem).GetProperty("ValueColumns");
        var valueColumns = valueColumnsProperty?.GetValue(firstItem) as Dictionary<string, decimal>;

        if (valueColumns != null && valueColumns.Any())
        {
            foreach (var pair in valueColumns)
            {
                result.Add((pair.Key, pair.Key));
            }
        }
        else
        {
            var properties = typeof(TItem).GetProperties()
                .Where(p => p.PropertyType == typeof(decimal) &&
                       p.Name != "SchoolName" &&
                       p.Name != "ValueColumns")
                .ToList();

            foreach (var prop in properties)
            {
                result.Add((prop.Name, GetDisplayName(prop.Name)));
            }
        }

        return result;
    }

    // 抽出通用 YValueSelector 方法
    private Func<TItem, string, decimal?> CreateYValueSelector()
    {
        return (item, propName) =>
        {
            if (item == null) return null;

            // 嘗試從 ValueColumns 抓取
            var valueColumnsProperty = typeof(TItem).GetProperty("ValueColumns");
            var valueColumns = valueColumnsProperty?.GetValue(item) as Dictionary<string, decimal>;
            if (valueColumns != null && valueColumns.ContainsKey(propName))
            {
                return valueColumns[propName];
            }

            // 再用反射抓取欄位
            var property = typeof(TItem).GetProperty(propName);
            if (property != null && property.PropertyType == typeof(decimal))
            {
                return (decimal?)property.GetValue(item);
            }

            return null;
        };
    }

    // 根據屬性名稱獲取圖表類型
    private SeriesType GetSeriesType(string propName)
    {
        // 如果是混合圖表且有對應的系列類型配置，則使用配置的類型
        if (IsMixedChart && SeriesTypes != null && SeriesTypes.ContainsKey(propName))
        {
            return SeriesTypes[propName];
        }

        // 否則使用預設圖表類型
        return ChartType;
    }

    // "按我唷"按鈕的互動視窗內容
    private RenderFragment ClickMeModalContent => builder =>
    {
        if (!isLoading && ChartData != null && ChartData.Any())
        {
            builder.OpenElement(0, "div");
            builder.AddAttribute(1, "style", "height: 400px;");

            builder.OpenComponent<ApexChart<TItem>>(2);
            builder.AddAttribute(3, "Options", ChartOptions);
            builder.AddAttribute(4, "Title", $"{Title} - 詳細檢視");

            if (!MultiSeries)
            {
                builder.OpenComponent<ApexPointSeries<TItem>>(5);
                builder.AddAttribute(6, "Name", SeriesName);
                builder.AddAttribute(7, "Items", ChartData);
                builder.AddAttribute(8, "XValue", XValueSelector);
                builder.AddAttribute(9, "YValue", YValueSelector);
                builder.AddAttribute(10, "SeriesType", ChartType);
                if (isShowDataLabels)
                {
                    builder.AddAttribute(11, "ShowDataLabels", true);
                }
                builder.CloseComponent();
            }
            else
            {
                // 多系列圖表的處理
                var ySelector = CreateYValueSelector();
                var valueProperties = GetValueProperties();
                int componentIndex = 12;

                foreach (var prop in valueProperties)
                {
                    var seriesType = GetSeriesType(prop.PropName);

                    builder.OpenComponent<ApexPointSeries<TItem>>(componentIndex++);
                    builder.AddAttribute(componentIndex++, "Name", prop.DisplayName);
                    builder.AddAttribute(componentIndex++, "Items", ChartData);
                    builder.AddAttribute(componentIndex++, "XValue", XValueSelector);
                    builder.AddAttribute(componentIndex++, "YValue", (Func<TItem, decimal?>)(item => ySelector(item, prop.PropName)));
                    builder.AddAttribute(componentIndex++, "SeriesType", seriesType);
                    if (isShowDataLabels)
                    {
                        builder.AddAttribute(componentIndex++, "ShowDataLabels", true);
                    }
                    builder.CloseComponent();
                }
            }

            builder.CloseComponent();
            builder.CloseElement();
        }
        else
        {
            builder.OpenElement(0, "div");
            builder.AddAttribute(1, "class", "text-center p-4");
            builder.OpenElement(2, "p");
            builder.AddContent(3, isLoading ? "正在載入圖表數據..." : "沒有可顯示的圖表數據");
            builder.CloseElement();
            builder.CloseElement();
        }
    };

}